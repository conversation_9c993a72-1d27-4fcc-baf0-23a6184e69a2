# GeBIZ Opportunities Scraper

A Python web scraper for extracting procurement opportunities from the Singapore Government's GeBIZ portal.

## Features

- Scrapes all opportunities from the GeBIZ opportunities page
- Handles pagination automatically
- Extracts key information: reference number, title, agency, category, dates, etc.
- Saves data in both JSON and CSV formats
- Uses Selenium for handling dynamic content
- Automatic Chrome driver management

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Make sure you have Chrome browser installed on your system.

## Usage

### Basic Usage

Run the main scraper:
```bash
python main.py
```

This will:
- Open Chrome browser (visible by default)
- Navigate through all pages of opportunities
- Extract data from each opportunity
- Save results to timestamped JSON and CSV files

### Testing and Experimentation

To examine the page structure and test the scraper:
```bash
python test_scraper.py
```

This will:
- Open the GeBIZ page in a visible browser
- Print information about page structure
- Save the page source for manual inspection
- Wait for user input before closing

## Configuration

### Headless Mode

To run the scraper without opening a visible browser window, modify the main function in `main.py`:

```python
scraper = GeBIZScraper(headless=True)  # Change to True
```

### Timeout Settings

Adjust wait times in the `GeBIZScraper` class if the website is slow:
- `WebDriverWait(self.driver, 10)` - Change 10 to a higher value
- `time.sleep(3)` calls - Increase sleep duration

## Output Files

The scraper generates timestamped files:
- `gebiz_opportunities_YYYYMMDD_HHMMSS.json` - JSON format
- `gebiz_opportunities_YYYYMMDD_HHMMSS.csv` - CSV format

## Data Fields

Each opportunity record contains:
- `reference_number` - Tender reference number
- `title` - Opportunity title
- `agency` - Government agency
- `category` - Procurement category
- `published_date` - Publication date
- `closing_date` - Submission deadline
- `status` - Current status (if available)
- `detail_link` - Link to detailed view (if available)
- `scraped_at` - Timestamp when data was scraped

## Troubleshooting

### Chrome Driver Issues
- The scraper uses webdriver-manager to automatically download the correct Chrome driver
- Ensure Chrome browser is installed and up to date

### Page Loading Issues
- Increase timeout values if the website is slow
- Check your internet connection
- The website might have anti-bot measures - try running with visible browser first

### No Data Scraped
- Run the test script to examine page structure
- The website structure might have changed
- Check if the website requires login or has access restrictions

## Customization

To modify the scraper for different data or websites:

1. Update the `extract_opportunity_data()` method to match the table structure
2. Modify the pagination logic in `navigate_to_next_page()` 
3. Adjust the selectors in `get_current_page_opportunities()`

## Legal Notice

This scraper is for educational and research purposes. Please:
- Respect the website's robots.txt and terms of service
- Don't overload the server with too many requests
- Use the data responsibly and in compliance with applicable laws
