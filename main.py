import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import pandas as pd

class GeBIZScraper:
    def __init__(self, headless=True):
        """Initialize the scraper with Chrome driver options"""
        self.chrome_options = Options()
        if headless:
            self.chrome_options.add_argument("--headless")
        self.chrome_options.add_argument("--no-sandbox")
        self.chrome_options.add_argument("--disable-dev-shm-usage")
        self.chrome_options.add_argument("--disable-gpu")
        self.chrome_options.add_argument("--window-size=1920,1080")

        self.driver = None
        self.wait = None
        self.opportunities = []

    def start_driver(self):
        """Start the Chrome driver"""
        try:
            # Use webdriver-manager to automatically handle driver installation
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=self.chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            print("Chrome driver started successfully")
        except Exception as e:
            print(f"Error starting Chrome driver: {e}")
            raise

    def close_driver(self):
        """Close the Chrome driver"""
        if self.driver:
            self.driver.quit()
            print("Chrome driver closed")

    def navigate_to_opportunities(self):
        """Navigate to the opportunities page"""
        url = "https://www.gebiz.gov.sg/ptn/opportunity/opportunityDetails.xhtml"
        print(f"Navigating to: {url}")
        self.driver.get(url)

        # Wait for page to load
        time.sleep(3)

        # Check if we need to handle any initial popups or cookies
        try:
            # Look for cookie acceptance or other popups
            cookie_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Accept') or contains(text(), 'OK')]")
            cookie_button.click()
            time.sleep(1)
        except NoSuchElementException:
            pass  # No cookie popup found

    def wait_for_table_load(self):
        """Wait for the opportunities table to load"""
        try:
            # Wait for the table or data container to be present
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            time.sleep(2)  # Additional wait for dynamic content
            return True
        except TimeoutException:
            print("Timeout waiting for table to load")
            return False

    def extract_opportunity_data(self, row_element):
        """Extract data from a single opportunity row"""
        try:
            opportunity = {}

            # Extract all cell data from the row
            cells = row_element.find_elements(By.TAG_NAME, "td")

            if len(cells) >= 6:  # Assuming at least 6 columns
                opportunity['reference_number'] = cells[0].text.strip()
                opportunity['title'] = cells[1].text.strip()
                opportunity['agency'] = cells[2].text.strip()
                opportunity['category'] = cells[3].text.strip()
                opportunity['published_date'] = cells[4].text.strip()
                opportunity['closing_date'] = cells[5].text.strip()

                # Try to extract additional details if available
                if len(cells) > 6:
                    opportunity['status'] = cells[6].text.strip()

                # Try to get the link to detailed view
                try:
                    link_element = row_element.find_element(By.TAG_NAME, "a")
                    opportunity['detail_link'] = link_element.get_attribute('href')
                except NoSuchElementException:
                    opportunity['detail_link'] = None

                opportunity['scraped_at'] = datetime.now().isoformat()

            return opportunity

        except Exception as e:
            print(f"Error extracting opportunity data: {e}")
            return None

    def get_current_page_opportunities(self):
        """Extract all opportunities from the current page"""
        opportunities = []

        try:
            # Find the table body or container with opportunities
            table_body = self.driver.find_element(By.TAG_NAME, "tbody")
            rows = table_body.find_elements(By.TAG_NAME, "tr")

            print(f"Found {len(rows)} rows on current page")

            for row in rows:
                opportunity = self.extract_opportunity_data(row)
                if opportunity and opportunity.get('reference_number'):
                    opportunities.append(opportunity)

        except NoSuchElementException:
            print("Could not find table body")

        return opportunities

    def navigate_to_next_page(self):
        """Navigate to the next page if available"""
        try:
            # Look for next page button - common patterns
            next_button_selectors = [
                "//a[contains(text(), 'Next')]",
                "//button[contains(text(), 'Next')]",
                "//a[contains(@class, 'next')]",
                "//button[contains(@class, 'next')]",
                "//a[@title='Next']",
                "//button[@title='Next']"
            ]

            for selector in next_button_selectors:
                try:
                    next_button = self.driver.find_element(By.XPATH, selector)
                    if next_button.is_enabled():
                        next_button.click()
                        time.sleep(3)  # Wait for page to load
                        return True
                except NoSuchElementException:
                    continue

            print("No next page button found or enabled")
            return False

        except Exception as e:
            print(f"Error navigating to next page: {e}")
            return False

    def scrape_all_opportunities(self):
        """Scrape all opportunities from all pages"""
        print("Starting to scrape opportunities...")

        if not self.wait_for_table_load():
            print("Failed to load initial page")
            return []

        page_number = 1

        while True:
            print(f"Scraping page {page_number}...")

            # Get opportunities from current page
            page_opportunities = self.get_current_page_opportunities()
            self.opportunities.extend(page_opportunities)

            print(f"Found {len(page_opportunities)} opportunities on page {page_number}")
            print(f"Total opportunities so far: {len(self.opportunities)}")

            # Try to navigate to next page
            if not self.navigate_to_next_page():
                print("No more pages available")
                break

            page_number += 1

            # Safety check to prevent infinite loops
            if page_number > 100:  # Adjust this limit as needed
                print("Reached maximum page limit")
                break

        print(f"Scraping completed. Total opportunities found: {len(self.opportunities)}")
        return self.opportunities

    def save_to_json(self, filename=None):
        """Save opportunities to JSON file"""
        if not filename:
            filename = f"gebiz_opportunities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.opportunities, f, indent=2, ensure_ascii=False)

        print(f"Data saved to {filename}")
        return filename

    def save_to_csv(self, filename=None):
        """Save opportunities to CSV file"""
        if not filename:
            filename = f"gebiz_opportunities_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        if self.opportunities:
            df = pd.DataFrame(self.opportunities)
            df.to_csv(filename, index=False, encoding='utf-8')
            print(f"Data saved to {filename}")
            return filename
        else:
            print("No data to save")
            return None

def main():
    """Main function to run the scraper"""
    scraper = GeBIZScraper(headless=False)  # Set to True for headless mode

    try:
        scraper.start_driver()
        scraper.navigate_to_opportunities()

        # Scrape all opportunities
        opportunities = scraper.scrape_all_opportunities()

        if opportunities:
            # Save to both JSON and CSV
            json_file = scraper.save_to_json()
            csv_file = scraper.save_to_csv()

            print(f"\nScraping Summary:")
            print(f"Total opportunities scraped: {len(opportunities)}")
            print(f"JSON file: {json_file}")
            print(f"CSV file: {csv_file}")

            # Display first few opportunities as sample
            print(f"\nSample opportunities:")
            for i, opp in enumerate(opportunities[:3]):
                print(f"{i+1}. {opp.get('title', 'N/A')} - {opp.get('agency', 'N/A')}")
        else:
            print("No opportunities were scraped")

    except Exception as e:
        print(f"Error during scraping: {e}")

    finally:
        scraper.close_driver()

if __name__ == "__main__":
    main()