#!/usr/bin/env python3
"""
Test script for GeBIZ scraper - allows for experimentation and debugging
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def test_page_structure():
    """Test script to examine the page structure"""
    
    # Setup Chrome driver
    chrome_options = Options()
    # chrome_options.add_argument("--headless")  # Comment out to see browser
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        print("Navigating to GeBIZ opportunities page...")
        driver.get("https://www.gebiz.gov.sg/ptn/opportunity/opportunityDetails.xhtml")
        
        # Wait for page to load
        time.sleep(5)
        
        print("Page title:", driver.title)
        print("Current URL:", driver.current_url)
        
        # Look for tables
        tables = driver.find_elements(By.TAG_NAME, "table")
        print(f"Found {len(tables)} tables on the page")
        
        # Look for common pagination elements
        pagination_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Next') or contains(text(), 'Previous') or contains(@class, 'page')]")
        print(f"Found {len(pagination_elements)} potential pagination elements")
        
        # Look for data rows
        rows = driver.find_elements(By.TAG_NAME, "tr")
        print(f"Found {len(rows)} table rows")
        
        # Print first few rows to understand structure
        for i, row in enumerate(rows[:5]):
            cells = row.find_elements(By.TAG_NAME, "td")
            if cells:
                print(f"Row {i}: {len(cells)} cells")
                for j, cell in enumerate(cells[:3]):  # First 3 cells
                    print(f"  Cell {j}: {cell.text[:50]}...")
        
        # Look for form elements that might be needed for search/filtering
        forms = driver.find_elements(By.TAG_NAME, "form")
        print(f"Found {len(forms)} forms")
        
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"Found {len(inputs)} input elements")
        
        # Check for any JavaScript-rendered content
        print("\nChecking for dynamic content...")
        time.sleep(3)
        
        # Re-check after waiting
        tables_after = driver.find_elements(By.TAG_NAME, "table")
        rows_after = driver.find_elements(By.TAG_NAME, "tr")
        print(f"After waiting: {len(tables_after)} tables, {len(rows_after)} rows")
        
        # Save page source for manual inspection
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Page source saved to page_source.html")
        
        input("Press Enter to close browser...")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    test_page_structure()
